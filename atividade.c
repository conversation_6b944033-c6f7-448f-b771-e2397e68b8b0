#include <stdio.h>
#include <stdlib.h>

struct disciplina {
    char nome[30];
    float nota1, nota2, media;
};

float mediaNotas(float nota1, float nota2) {
    return (nota1 + nota2) / 2;
}

void compararNotas (struct disciplina * primeiraDisciplina, struct disciplina * segundaDisciplina) {
    if (primeiraDisciplina->media > segundaDisciplina->media) {
        printf("A disciplina %s teve a maior media\n", primeiraDisciplina->nome);
    } else if (primeiraDisciplina->media < segundaDisciplina->media) {
        printf("A disciplina %s teve a maior media\n", segundaDisciplina->nome);
    } else {
        printf("As disciplinas tiveram a mesma media\n");
    }
}

void inserirDisciplina() {
    struct disciplina primeiraDisciplina, segundaDisciplina;

    printf("Digite o nome da 1 disciplina: ");
    scanf("%s", primeiraDisciplina.nome);
    printf("Digite a nota 1 da 1 disciplina: ");
    scanf("%f", &primeiraDisciplina.nota1);
    printf("Digite a nota 2 da 1 disciplina: ");
    scanf("%f", &primeiraDisciplina.nota2);
    primeiraDisciplina.media = mediaNotas(primeiraDisciplina.nota1, primeiraDisciplina.nota2);

    printf("Digite o nome da 2 disciplina: ");
    scanf("%s", segundaDisciplina.nome);
    printf("Digite a nota 1 da 2 disciplina: ");
    scanf("%f", &segundaDisciplina.nota1);
    printf("Digite a nota 2 da 2 disciplina: ");
    scanf("%f", &segundaDisciplina.nota2);
    segundaDisciplina.media = mediaNotas(segundaDisciplina.nota1, segundaDisciplina.nota2);

    compararNotas(&primeiraDisciplina, &segundaDisciplina);
}

int main() {
    int opcao;
    do {
        printf("Escolha uma opcao:\n");
        printf("1 - Inserir disciplinas\n");
        printf("2 - Sair\n");
        scanf("%d", &opcao);

        switch(opcao){
            case 1:
                inserirDisciplina();
                break;
            case 2:
                printf("Saindo...\n");
                break;
            default:
                printf("Opcao invalida\n");
                break;
        }
    } while (opcao != 2);

    return 0;
}
