#include <stdio.h>
#include <stdlib.h>
#include <time.h> 

int i, j;

void Tabuleiro(char tabuleiro[8][8]) {
    int i, j;
    for(i = 0; i < 8; i++){
        for(j = 0; j < 8; j++){
            if(i < 3) {
                if((i + j) % 2 == 0)
                    tabuleiro[i][j] = 'X';
                else
                    tabuleiro[i][j] = '.';
            }
            else if(i > 4) {
                if((i + j) % 2 == 0)
                    tabuleiro[i][j] = 'O';
                else
                    tabuleiro[i][j] = '.';
            }
            else {
                tabuleiro[i][j] = '.';
            }
        }
    }
}

void imprimirTabuleiro(char tabuleiro[8][8]) {
    int i, j;
    for(i = 0; i < 8; i++){
        for(j = 0; j < 8; j++){
            printf("%c ", tabuleiro[i][j]);
        }
        printf("\n");
    }
}

int moverPecas(char tabuleiro[8][8]) {
    int i, j;
    for(i = 0; i < 8; i++){
        for(j = 0; j < 8; j++){
            if(tabuleiro[i][j] == 'X'){
                if(i + 1 < 8 && j + 1 < 8 && tabuleiro[i + 1][j + 1] == '.'){
                    tabuleiro[i + 1][j + 1] = 'X';
                    tabuleiro[i][j] = '.';
                }
            }
        }
    }
    return 0;
}

int main(){
    char tabuleiro[8][8];
    Tabuleiro(tabuleiro);
    imprimirTabuleiro(tabuleiro);
    return 0;
}