#include <stdio.h>

void soma() {
    float n1, n2;
    printf("Digite o primeiro numero: ");
    scanf("%f", &n1);
    printf("Digite o segundo numero: ");
    scanf("%f", &n2);
    printf("%.2f + %.2f = %.2f\n", n1, n2, n1 + n2);
}

void subtracao() {
    float n1, n2;
    printf("Digite o primeiro numero: ");
    scanf("%f", &n1);
    printf("Digite o segundo numero: ");
    scanf("%f", &n2);
    printf("%.2f - %.2f = %.2f\n", n1, n2, n1 - n2);
}

void multiplicacao() {
    float n1, n2;
    printf("Digite o primeiro numero: ");
    scanf("%f", &n1);
    printf("Digite o segundo numero: ");
    scanf("%f", &n2);
    printf("%.2f * %.2f = %.2f\n", n1, n2, n1 * n2);
}

void divisao() {
    float n1, n2;
    printf("Digite o primeiro numero: ");
    scanf("%f", &n1);
    printf("Digite o segundo numero: ");
    scanf("%f", &n2);
    if (n2 != 0)
        printf("%.2f / %.2f = %.2f\n", n1, n2, n1 / n2);
    else
        printf("Divisao por zero nao permitida.\n");
}

void calculadora() {
    char opcao;

    printf("\n=== CALCULADORA ===\n");
    printf("Escolha uma opcao:\n");
    printf("+  Soma\n");
    printf("-  Subtracao\n");
    printf("*  Multiplicacao\n");
    printf("/  Divisao\n");
    scanf(" %c", &opcao);

    switch(opcao){ 
        case '+': 
            soma(); 
            break;
        case '-': 
            subtracao(); 
            break;
        case '*': 
            multiplicacao(); 
            break;
        case '/': 
            divisao(); 
            break;
        default:  
            printf("Opcao invalida!\n");
            calculadora();
            break;
        }
}

void menuPrincipal() {
    int opcao;
    do {
        printf("\n=== MENU PRINCIPAL ===\n");
        printf("1 - Calculadora\n");
        printf("2 - Sair\n");
        scanf("%d", &opcao);

        switch(opcao){
            case 1:
                calculadora();
                break;
            case 2:
                printf("Saindo...\n");
                break;
            default:
                printf("Opcao invalida!\n");
        }
    } while (opcao != 2);
}

int main() {
    menuPrincipal();
    return 0;
}
